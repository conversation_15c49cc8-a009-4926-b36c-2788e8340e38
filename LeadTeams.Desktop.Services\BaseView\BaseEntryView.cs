﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseEntryView : LeadTeamsForm
    {
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        protected bool IsFormLoaded { get; set; } = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        protected bool SuspendDataChanged { get; set; } = false;
        private bool _isDataChanged = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsDataChanged
        {
            get => _isDataChanged;
            private set
            {
                if (_isDataChanged != value)
                {
                    _isDataChanged = value;
                    tsbtnSave.BackColor = value ? Color.Orange : tsbtnNew.BackColor;
                }
            }
        }

        public BaseEntryView()
        {
            InitializeComponent();

            AssociateAndRaiseEvents();

            ShowNewButtons(IsNewable);
            ShowDeleteButtons(IsDeleteable);
            ShowPrintButtons(IsPrintable);
            ShowTempSaveButtons(TempSaveable);
        }

        private void AssociateAndRaiseEvents()
        {
            // Use AsyncButtonHandler to prevent multiple clicks on buttons that trigger potentially long operations
            tsbtnNew.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                await Task.Run(() => this.SafelyInvokeAction(() =>
                {
                    SuspendDataChanged = true;
                    NewFunction();
                    SuspendDataChanged = false;
                }));
            });

            tsbtnSave.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                if (BaseSession.CheckActionAuthorization(this.Name, isEdit ? Actions.Edit : Actions.Add))
                    await Task.Run(() => this.SafelyInvokeAction(() => SaveFunction()));
            });

            tsbtnSaveAndPrint.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                if (BaseSession.CheckActionAuthorization(this.Name, isEdit ? Actions.Edit : Actions.Add) && BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
                {
                    await Task.Run(() => SaveFunction());
                    if (IsPrintable)
                        await Task.Run(() => this.SafelyInvokeAction(() => PrintFunction()));
                }
            });

            tsbtnTempSave.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                await Task.Run(() => this.SafelyInvokeAction(() => TempSaveFunction()));
            });

            tsbtnTempList.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                await Task.Run(() => this.SafelyInvokeAction(() => TempListFunction()));
            });

            tsbtnDelete.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                if (BaseSession.CheckActionAuthorization(this.Name, Actions.Delete))
                    await Task.Run(() => this.SafelyInvokeAction(() => DeleteFunction()));
            });

            tsbtnPrint.HandleAsyncClickSafe(async () =>
            {
                this.tsMain.Focus();
                if (BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
                    await Task.Run(() => this.SafelyInvokeAction(() => PrintFunction()));
            });

            tsbtnClose.Click += (s, e) => this.Close();

            this.Shown += (s, e) => IsFormLoaded = true;

            // Attach watcher after form is loaded
            this.Load += (s, e) => WatchForDataChanges(this);
            this.FormClosing += (s, e) =>
            {
                if (IsDataChanged)
                {
                    var result = MessageBox.Show("You have unsaved changes. Are you sure you want to close without saving?", "Confirm Close", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (result == DialogResult.No)
                        e.Cancel = true;
                }
            };
        }

        #region Buttons Actions Events
        protected virtual void NewFunction() { }
        protected virtual void SaveFunction() { }
        protected virtual void TempSaveFunction() { }
        protected virtual void TempListFunction() { }
        protected virtual void DeleteFunction() { }
        protected virtual void PrintFunction() { }
        #endregion

        #region Showing Buttons
        #region Fields - Properties
        private bool _isNewable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsNewable { get => _isNewable; set => ShowNewButtons(value); }
        private bool _isDeleteable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsDeleteable { get => _isDeleteable; set => ShowDeleteButtons(value); }
        private bool _isPrintable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsPrintable { get => _isPrintable; set => ShowPrintButtons(value); }
        private bool _tempSaveable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool TempSaveable { get => _tempSaveable; set => ShowTempSaveButtons(value); }
        #endregion

        #region Methods
        private void ShowNewButtons(bool value)
        {
            _isNewable = value;
            tsbtnNew.Visible = value;
            tssNew.Visible = value;
        }
        private void ShowDeleteButtons(bool value)
        {
            _isDeleteable = value;
            tsbtnDelete.Visible = value;
            tssDelete.Visible = value;
        }
        private void ShowPrintButtons(bool value)
        {
            _isPrintable = value;
            tsbtnSaveAndPrint.Visible = value;
            tssSaveAndPrint.Visible = value;
            tsbtnPrint.Visible = value;
            tssPrint.Visible = value;
        }
        private void ShowTempSaveButtons(bool value)
        {
            _tempSaveable = value;
            tsbtnTempSave.Visible = value;
            tssTempSave.Visible = value;
            tsbtnTempList.Visible = value;
            tssTempList.Visible = value;
        }
        #endregion
        #endregion

        #region Implement ILeadTeamsForm Interface
        //Properties
        private Ulid id { get; set; } = Ulid.Empty;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Ulid Id { get => id; set => id = value; }
        public bool isEdit = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsEdit
        {
            get => isEdit;
            set
            {
                isEdit = value;
                IsEditFunction(value);
            }
        }
        public bool isSuccessful { get; set; } = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsSuccessful { get => isSuccessful; set => isSuccessful = value; }
        #endregion

        protected virtual void ClearAndNew()
        {
            id = Ulid.Empty;
            IsEdit = false;
            isSuccessful = false;
            IsDataChanged = false;
        }
        //protected virtual void BindControls() { }
        protected virtual void LoadAllLists() { }
        protected virtual void EditModelBeforeSave() { }
        protected virtual void IsEditFunction(bool value) { }

        private void WatchForDataChanges(Control parent)
        {
            if (parent == null) parent = this;
            foreach (Control ctrl in parent.Controls)
            {
                if (ctrl is TextBox tb)
                    tb.TextChanged += (s, e) => SetDataChanged();
                else if (ctrl is ComboBox cb)
                    cb.SelectedIndexChanged += (s, e) => SetDataChanged();
                else if (ctrl is DateTimePicker dtp)
                    dtp.ValueChanged += (s, e) => SetDataChanged();
                else if (ctrl is CheckBox chk)
                    chk.CheckedChanged += (s, e) => SetDataChanged();
                else if (ctrl is RadioButton rb)
                    rb.CheckedChanged += (s, e) => SetDataChanged();
                if (ctrl.HasChildren)
                    WatchForDataChanges(ctrl);
            }
        }

        private void SetDataChanged()
        {
            if (IsFormLoaded && !SuspendDataChanged)
                IsDataChanged = true;
        }
    }
}
