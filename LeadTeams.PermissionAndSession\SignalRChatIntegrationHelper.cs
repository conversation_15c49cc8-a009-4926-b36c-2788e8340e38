﻿namespace LeadTeams.PermissionAndSession
{
    public static class SignalRChatIntegrationHelper
    {
        public static class URLs
        {
            public static string BaseURL
            {
                get
                {
#if DEBUG
                    return "https://localhost:7038";
#else
                    return "http://api-leadteams-test.runasp.net";
#endif
                }
            }
            public static string LeadTeams => BaseURL + EndPoints.LeadTeams;
        }

        public static class HUBs
        {
            public const string DuplicationLogin = "DuplicationLogin";
            public const string Ping = "Ping";
            public const string Pong = "Pong";

            public const string GetConnectedClients = "GetConnectedClients";

            public const string DatabaseChanges = "DatabaseChanges";
            public const string OnDatabaseChanges = "OnDatabaseChanges";
        }

        public static class EndPoints
        {
            public const string LeadTeams = "/SignalRChat/Hubs/LeadTeams";
        }
    }
}
