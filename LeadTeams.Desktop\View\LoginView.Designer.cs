﻿namespace LeadTeams.Desktop.View
{
    partial class LoginView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlp = new LeadTeamsTableLayoutPanel();
            lblHeader = new LeadTeamsLabel();
            lblUserName = new LeadTeamsLabel();
            lblPassword = new LeadTeamsLabel();
            tableLayoutPanel2 = new LeadTeamsTableLayoutPanel();
            btnLogin = new LeadTeamsButton();
            btnExit = new LeadTeamsButton();
            txtUserName = new LeadTeamsTextBox();
            txtPassword = new LeadTeamsTextBox();
            cbxSaveLoginData = new LeadTeamsCheckBox();
            tlp.SuspendLayout();
            tableLayoutPanel2.SuspendLayout();
            SuspendLayout();
            // 
            // tlp
            // 
            tlp.BackColor = Color.FromArgb(234, 242, 248);
            tlp.ColumnCount = 3;
            tlp.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 179F));
            tlp.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 422F));
            tlp.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlp.Controls.Add(lblHeader, 0, 0);
            tlp.Controls.Add(lblUserName, 0, 1);
            tlp.Controls.Add(lblPassword, 0, 2);
            tlp.Controls.Add(tableLayoutPanel2, 1, 4);
            tlp.Controls.Add(txtUserName, 1, 1);
            tlp.Controls.Add(txtPassword, 1, 2);
            tlp.Controls.Add(cbxSaveLoginData, 1, 3);
            tlp.Dock = DockStyle.Fill;
            tlp.ForeColor = Color.FromArgb(22, 71, 117);
            tlp.Location = new Point(0, 0);
            tlp.Margin = new Padding(4, 5, 4, 5);
            tlp.Name = "tlp";
            tlp.RightToLeft = RightToLeft.No;
            tlp.RowCount = 5;
            tlp.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
            tlp.RowStyles.Add(new RowStyle(SizeType.Absolute, 64F));
            tlp.RowStyles.Add(new RowStyle(SizeType.Absolute, 64F));
            tlp.RowStyles.Add(new RowStyle(SizeType.Absolute, 64F));
            tlp.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
            tlp.Size = new Size(615, 353);
            tlp.TabIndex = 1;
            // 
            // lblHeader
            // 
            lblHeader.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            lblHeader.BackColor = Color.FromArgb(234, 242, 248);
            tlp.SetColumnSpan(lblHeader, 2);
            lblHeader.FlatStyle = FlatStyle.Flat;
            lblHeader.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblHeader.ForeColor = Color.FromArgb(22, 71, 117);
            lblHeader.Location = new Point(4, 0);
            lblHeader.Margin = new Padding(4, 0, 4, 0);
            lblHeader.MinimumSize = new Size(56, 32);
            lblHeader.Name = "lblHeader";
            lblHeader.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblHeader.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            lblHeader.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblHeader.RadiusProperties.BorderColor = Color.Transparent;
            lblHeader.RadiusProperties.BorderRadius = 10;
            lblHeader.RadiusProperties.BorderSize = 0;
            lblHeader.RightToLeft = RightToLeft.No;
            lblHeader.Size = new Size(593, 80);
            lblHeader.TabIndex = 0;
            lblHeader.Text = "Login Form";
            lblHeader.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblUserName
            // 
            lblUserName.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            lblUserName.AutoSize = true;
            lblUserName.BackColor = Color.FromArgb(234, 242, 248);
            lblUserName.FlatStyle = FlatStyle.Flat;
            lblUserName.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblUserName.ForeColor = Color.FromArgb(22, 71, 117);
            lblUserName.Location = new Point(4, 96);
            lblUserName.Margin = new Padding(4, 0, 4, 0);
            lblUserName.MinimumSize = new Size(56, 32);
            lblUserName.Name = "lblUserName";
            lblUserName.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblUserName.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            lblUserName.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblUserName.RadiusProperties.BorderColor = Color.Transparent;
            lblUserName.RadiusProperties.BorderRadius = 10;
            lblUserName.RadiusProperties.BorderSize = 0;
            lblUserName.RightToLeft = RightToLeft.No;
            lblUserName.Size = new Size(171, 32);
            lblUserName.TabIndex = 0;
            lblUserName.Text = "Email";
            // 
            // lblPassword
            // 
            lblPassword.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            lblPassword.AutoSize = true;
            lblPassword.BackColor = Color.FromArgb(234, 242, 248);
            lblPassword.FlatStyle = FlatStyle.Flat;
            lblPassword.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblPassword.ForeColor = Color.FromArgb(22, 71, 117);
            lblPassword.Location = new Point(4, 160);
            lblPassword.Margin = new Padding(4, 0, 4, 0);
            lblPassword.MinimumSize = new Size(56, 32);
            lblPassword.Name = "lblPassword";
            lblPassword.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            lblPassword.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            lblPassword.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            lblPassword.RadiusProperties.BorderColor = Color.Transparent;
            lblPassword.RadiusProperties.BorderRadius = 10;
            lblPassword.RadiusProperties.BorderSize = 0;
            lblPassword.RightToLeft = RightToLeft.No;
            lblPassword.Size = new Size(171, 32);
            lblPassword.TabIndex = 0;
            lblPassword.Text = "Password";
            // 
            // tableLayoutPanel2
            // 
            tableLayoutPanel2.BackColor = Color.FromArgb(234, 242, 248);
            tableLayoutPanel2.ColumnCount = 2;
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel2.Controls.Add(btnLogin, 1, 0);
            tableLayoutPanel2.Controls.Add(btnExit, 0, 0);
            tableLayoutPanel2.Dock = DockStyle.Fill;
            tableLayoutPanel2.ForeColor = Color.FromArgb(22, 71, 117);
            tableLayoutPanel2.Location = new Point(183, 277);
            tableLayoutPanel2.Margin = new Padding(4, 5, 4, 5);
            tableLayoutPanel2.Name = "tableLayoutPanel2";
            tableLayoutPanel2.RightToLeft = RightToLeft.No;
            tableLayoutPanel2.RowCount = 1;
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tableLayoutPanel2.Size = new Size(414, 71);
            tableLayoutPanel2.TabIndex = 1;
            // 
            // btnLogin
            // 
            btnLogin.BackColor = Color.FromArgb(22, 71, 117);
            btnLogin.Dock = DockStyle.Fill;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnLogin.ForeColor = Color.FromArgb(234, 242, 248);
            btnLogin.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnLogin.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnLogin.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnLogin.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnLogin.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnLogin.Location = new Point(211, 5);
            btnLogin.Margin = new Padding(4, 5, 4, 5);
            btnLogin.MinimumSize = new Size(56, 32);
            btnLogin.Name = "btnLogin";
            btnLogin.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnLogin.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnLogin.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnLogin.RadiusProperties.BorderColor = Color.Transparent;
            btnLogin.RadiusProperties.BorderRadius = 10;
            btnLogin.RadiusProperties.BorderSize = 0;
            btnLogin.RightToLeft = RightToLeft.No;
            btnLogin.Size = new Size(199, 61);
            btnLogin.TabIndex = 0;
            btnLogin.Text = "Login";
            btnLogin.UseVisualStyleBackColor = false;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(31, 111, 139);
            btnExit.Dock = DockStyle.Fill;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btnExit.ForeColor = Color.FromArgb(234, 242, 248);
            btnExit.ImagesProperties.BackgroundImageSize = new Size(18, 18);
            btnExit.ImagesProperties.ImageFixedSize = new Size(24, 24);
            btnExit.ImagesProperties.ImageMaxSize = new Size(24, 24);
            btnExit.ImagesProperties.ImageSizeMode = Desktop.Controls.Core.Helper.Enums.SizeMode.Stretch;
            btnExit.ImagesProperties.OriginalImageSize = new Size(18, 18);
            btnExit.Location = new Point(4, 5);
            btnExit.Margin = new Padding(4, 5, 4, 5);
            btnExit.MinimumSize = new Size(56, 32);
            btnExit.Name = "btnExit";
            btnExit.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color5;
            btnExit.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnExit.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnExit.RadiusProperties.BorderColor = Color.Transparent;
            btnExit.RadiusProperties.BorderRadius = 10;
            btnExit.RadiusProperties.BorderSize = 0;
            btnExit.RightToLeft = RightToLeft.No;
            btnExit.Size = new Size(199, 61);
            btnExit.TabIndex = 0;
            btnExit.Text = "Exit";
            btnExit.UseVisualStyleBackColor = false;
            // 
            // txtUserName
            // 
            txtUserName.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtUserName.BackColor = Color.FromArgb(234, 242, 248);
            txtUserName.BorderColor = Color.FromArgb(180, 180, 180);
            txtUserName.EdgeColor = Color.White;
            txtUserName.Font = new Font("Tahoma", 11F);
            txtUserName.ForeColor = Color.FromArgb(22, 71, 117);
            txtUserName.Location = new Point(183, 98);
            txtUserName.Margin = new Padding(4, 5, 4, 5);
            txtUserName.MaxLength = 32767;
            txtUserName.Name = "txtUserName";
            txtUserName.Size = new Size(414, 28);
            txtUserName.TabIndex = 2;
            txtUserName.TextAlignment = HorizontalAlignment.Left;
            // 
            // txtPassword
            // 
            txtPassword.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtPassword.BackColor = Color.FromArgb(234, 242, 248);
            txtPassword.BorderColor = Color.FromArgb(180, 180, 180);
            txtPassword.EdgeColor = Color.White;
            txtPassword.Font = new Font("Tahoma", 11F);
            txtPassword.ForeColor = Color.FromArgb(22, 71, 117);
            txtPassword.Location = new Point(183, 162);
            txtPassword.Margin = new Padding(4, 5, 4, 5);
            txtPassword.MaxLength = 32767;
            txtPassword.Name = "txtPassword";
            txtPassword.Size = new Size(414, 28);
            txtPassword.TabIndex = 2;
            txtPassword.TextAlignment = HorizontalAlignment.Left;
            txtPassword.UsePassword = true;
            // 
            // cbxSaveLoginData
            // 
            cbxSaveLoginData.Anchor = AnchorStyles.Left;
            cbxSaveLoginData.AutoSize = true;
            cbxSaveLoginData.BackColor = Color.FromArgb(234, 242, 248);
            cbxSaveLoginData.CheckedColor = Color.FromArgb(64, 158, 255);
            tlp.SetColumnSpan(cbxSaveLoginData, 2);
            cbxSaveLoginData.DisabledColor = Color.FromArgb(196, 198, 202);
            cbxSaveLoginData.DisabledStringColor = Color.FromArgb(186, 187, 189);
            cbxSaveLoginData.Enable = true;
            cbxSaveLoginData.EnabledCheckedColor = Color.FromArgb(64, 158, 255);
            cbxSaveLoginData.EnabledStringColor = Color.FromArgb(153, 153, 153);
            cbxSaveLoginData.EnabledUncheckedColor = Color.FromArgb(156, 158, 161);
            cbxSaveLoginData.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSaveLoginData.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSaveLoginData.Location = new Point(183, 227);
            cbxSaveLoginData.Margin = new Padding(4, 5, 4, 5);
            cbxSaveLoginData.Name = "cbxSaveLoginData";
            cbxSaveLoginData.RightToLeft = RightToLeft.No;
            cbxSaveLoginData.Size = new Size(151, 20);
            cbxSaveLoginData.TabIndex = 3;
            cbxSaveLoginData.Text = "Save Login Data";
            cbxSaveLoginData.UseVisualStyleBackColor = false;
            // 
            // LoginView
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(615, 353);
            Controls.Add(tlp);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Margin = new Padding(6);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "LoginView";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "LoginView";
            tlp.ResumeLayout(false);
            tlp.PerformLayout();
            tableLayoutPanel2.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsTableLayoutPanel tlp;
        private LeadTeamsLabel lblHeader;
        private LeadTeamsLabel lblUserName;
        private LeadTeamsLabel lblPassword;
        private LeadTeamsTableLayoutPanel tableLayoutPanel2;
        public LeadTeamsButton btnLogin;
        private LeadTeamsButton btnExit;
        public LeadTeamsTextBox txtUserName;
        public LeadTeamsTextBox txtPassword;
        private LeadTeamsCheckBox cbxSaveLoginData;
    }
}