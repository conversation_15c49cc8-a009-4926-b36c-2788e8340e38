﻿namespace LeadTeams.Services.Core.Business
{
    public interface ILoginSessionService
    {
        Task<List<LoginSessionModel>> GetActiveLoginSessionsAsync();
        Task<List<LoginSessionModel>> GetLoginSessions(Ulid userId, string appName);
        Task<LoginSessionModel?> GetLoginSessionWithDeviceId(Ulid userId, string appName, string deviceId);
        Task<LoginSessionModel?> GetLoginSessionWithSessionToken(Ulid userId, string appName, string sessionToken);
        Task AddLoginSession(LoginSessionModel loginSession);
        Task UpdateLoginSession(LoginSessionModel loginSession);
        Task InactiveLoginSession(Ulid userId, string appName, string sessionToken);
        Task RemoveLoginSession(Ulid userId, string appName, string sessionToken);
    }
}
