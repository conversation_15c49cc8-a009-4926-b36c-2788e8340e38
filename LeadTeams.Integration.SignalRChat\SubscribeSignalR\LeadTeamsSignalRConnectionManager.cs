﻿namespace LeadTeams.Integration.SignalRChat.SubscribeSignalR
{
    public class LeadTeamsSignalRConnectionManager : SignalRConnectionManager, ILeadTeamsSignalRConnectionManager, IDisposable, IAsyncDisposable
    {
        public delegate void DuplicationLoginEventHandler();
        public static event DuplicationLoginEventHandler? OnDuplicationLogin;

        public LeadTeamsSignalRConnectionManager(ISession session, ILogger logger) : base(session, logger)
        {
        }

        protected override void InitialEvents()
        {
            base.InitialEvents();

            Connection.On(SignalRChatIntegrationHelper.HUBs.OnDatabaseChanges,
                (string dataBaseEntityString) => DataBaseWatcher.NotifyDataBaseWatcherChanged(dataBaseEntityString));
            Connection.On(SignalRChatIntegrationHelper.HUBs.DuplicationLogin,
                () => OnDuplicationLogin?.Invoke());
            Connection.On(SignalRChatIntegrationHelper.HUBs.Ping,
                async () => await Connection.InvokeCoreAsync(SignalRChatIntegrationHelper.HUBs.Pong, new object[] { }));
        }

        public async Task<Dictionary<string, ClientDto>> GetConnectedClientsAsync()
        {
            try
            {
                return IsConnected ? await Connection.GetConnectedClients() : new Dictionary<string, ClientDto>();
            }
            catch (Exception ex)
            {
                return new Dictionary<string, ClientDto>();
            }
        }

        public async Task NotifyDatabaseChangesAsync(DataBaseEntity dataBaseEntity)
        {
            if (IsConnected)
            {
                string dataBaseEntityString = JsonConvert.SerializeObject(dataBaseEntity, Formatting.Indented, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                });
                await Connection.DatabaseChanges(dataBaseEntityString);
            }
        }

        bool _disposed = false;
        public async ValueTask DisposeAsync()
        {
            if (_disposed) return;

            _disposed = true;
            if (Connection != null)
            {
                await Connection.DisposeAsync();
            }
        }

        public void Dispose()
        {
            DisposeAsync().AsTask().Wait();
        }
    }
}
