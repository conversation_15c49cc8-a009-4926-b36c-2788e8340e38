﻿namespace LeadTeams.API.Hubs
{
    [Authorize]
    public class LeadTeamsHub : SignalRChatHub
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRepository _repository;
        private readonly ILoginSessionService _loginSessionService;
        private readonly Serilog.ILogger _logger;

        public static ConcurrentDictionary<Ulid, (string, DateTime)> CurrentConnections = new ConcurrentDictionary<Ulid, (string, DateTime)>();

        public LeadTeamsHub(IUnitOfWork unitOfWork, IRepository repository, ILoginSessionService loginSessionService, Serilog.ILogger logger) : base(repository, logger)
        {
            _unitOfWork = unitOfWork;
            _repository = repository;
            _loginSessionService = loginSessionService;
            _logger = logger;
        }

        private LoginSessionModel CreateLoginSessionModel => new LoginSessionModel()
        {
            DeviceId = GetDeviceId,
            SessionToken = Context.ConnectionId,
            SessionDateTime = DateTime.UtcNow,
            SessionLastDateTime = DateTime.UtcNow,
            SessionStatus = SessionStatusEnumeration.Active.Name,
            SessionHostType = GetHostType,
            SessionAppName = GetAppName,
            EmployeeId = ValidateValue.ValidateUlid(GetUserId),
            OrganizationId = ValidateValue.ValidateUlid(GetOrganizationId),
        };

        public override async Task OnConnectedAsync()
        {
            try
            {
                var loginSession = await _loginSessionService.GetLoginSessionWithDeviceId(ValidateValue.ValidateUlid(GetUserId), GetAppName, GetDeviceId);
                if (loginSession != null)
                {
                    loginSession.SessionToken = Context.ConnectionId;
                    loginSession.SessionLastDateTime = DateTime.UtcNow;
                    loginSession.SessionStatus = SessionStatusEnumeration.Active.Name;
                    await _loginSessionService.UpdateLoginSession(loginSession);
                }
                else
                {
                    if (this is Hub hub)
                        await hub.Clients.Caller.SendCoreAsync(SignalRChatIntegrationHelper.HUBs.DuplicationLogin, new object[] { });
                    await _loginSessionService.AddLoginSession(CreateLoginSessionModel);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, ex.Message);
            }
            finally
            {
                CurrentConnections.AddOrUpdate(
                    ValidateValue.ValidateUlid(GetUserId),
                    (Context.ConnectionId, DateTime.UtcNow),
                    (id, existingConnectionId) =>
                    {
                        // If the connection already exists, return the existing connection ID
                        return existingConnectionId;
                    });
                await base.OnConnectedAsync();
            }
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            try
            {
                await _loginSessionService.InactiveLoginSession(ValidateValue.ValidateUlid(GetUserId), GetAppName, Context.ConnectionId);
                CurrentConnections.TryRemove(ValidateValue.ValidateUlid(GetUserId), out _);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, ex.Message);
            }
            finally
            {
                await base.OnDisconnectedAsync(exception);
            }
        }

        public async Task<bool> DatabaseChanges(string dataBaseEntityString)
        {
            try
            {
                var hub = this as Hub;
                if (hub != null)
                {
                    await hub.Clients.All.SendCoreAsync(SignalRChatIntegrationHelper.HUBs.OnDatabaseChanges, new object[] { dataBaseEntityString });
                    return await Task.FromResult(true);
                }
                return await Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, ex.Message);
                return await Task.FromResult(false);
            }
        }

        public override async Task<LoginResponse?> Login(SignalRChat.Core.Modules.Authentication.Login.LoginRequest request)
        {
            LoginResponse? login = await base.Login(request);
            if (login != null)
                foreach (ClientDto client in login.Users)
                {
                    List<MessageModel> messages = _unitOfWork.Message.GetMessagesForEmployee(client.ClientId);
                    client.ReceivedMessages = (await ConvertToChatMessage(messages)).Where(x => x.ReceiverId == client.ClientId).ToList();
                    client.SentMessages = (await ConvertToChatMessage(messages)).Where(x => x.SenderId == client.ClientId).ToList();
                }
            return login;
        }

        public override async Task<bool> Logout(LogoutRequest request)
        {
            bool logout = await base.Logout(request);
            if (logout)
                await _loginSessionService.InactiveLoginSession(ValidateValue.ValidateUlid(GetUserId), GetAppName, Context.ConnectionId);
            return logout;
        }

        public override async Task<bool> TextMessage(TextMessageRequest request)
        {
            bool result = await base.TextMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                {
                    byte[]? message = Converters.ToByteArray(request.Message);
                    if (message != null)
                        await SaveMessageToDatabase(Enums.MessageType.Text, message, receiverId.ClientId);
                }
            }
            return result;
        }

        public override async Task<bool> ImageMessage(ImageMessageRequest request)
        {
            bool result = await base.ImageMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Picture, request.Image, receiverId.ClientId);
            }
            return result;
        }

        public override async Task<bool> RecordMessage(RecordMessageRequest request)
        {
            bool result = await base.RecordMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Record, request.Record, receiverId.ClientId);
            }
            return result;
        }

        public override async Task<bool> BuzzMessage(BuzzMessageRequest request)
        {
            bool result = await base.BuzzMessage(request);
            if (result)
            {
                var receiverId = await GetClient(request.ReceiverId, false);
                if (receiverId != null)
                    await SaveMessageToDatabase(Enums.MessageType.Buzz, Array.Empty<byte>(), receiverId.ClientId);
            }
            return result;
        }

        private async Task SaveMessageToDatabase(Enums.MessageType messageType, byte[] messageContent, Ulid employeeToId)
        {
            try
            {
                MessageModel message = new MessageModel()
                {
                    MessageDate = DateTime.Now,
                    MessageType = (int)messageType,
                    MessageContent = messageContent,
                    EmployeeId = employeeToId,
                    MessageMediaMimeType = null,
                    MessageIsRead = false,
                    EmployeeFromId = ValidateValue.ValidateUlid(GetUserId),
                    EmployeeToId = employeeToId,
                    OrganizationId = ValidateValue.ValidateUlid(GetOrganizationId),
                };
                new BaseValidation().Validate(message);
                await _unitOfWork.Message.AddAsync(message);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, ex.Message);
            }
        }

        protected virtual string GetUserId => Context.GetHttpContext()?.Request.Headers["UserId"].FirstOrDefault() ?? throw new NullReferenceException("User must not be null");
        protected virtual string GetOrganizationId => Context.GetHttpContext()?.Request.Headers["OrganizationId"].FirstOrDefault() ?? throw new NullReferenceException("Organization must not be null");
        protected virtual string GetDeviceId => Context.GetHttpContext()?.Request.Headers["DeviceId"].FirstOrDefault() ?? throw new NullReferenceException("Device Id must not be null");
        protected virtual string GetHostType => Context.GetHttpContext()?.Request.Headers["HostType"].FirstOrDefault() ?? throw new NullReferenceException("Host Type must not be null");
        protected virtual string GetAppName => Context.GetHttpContext()?.Request.Headers["AppName"].FirstOrDefault() ?? throw new NullReferenceException("App Name must not be null");

        private async Task<List<ChatMessageDto>> ConvertToChatMessage(List<MessageModel> messages)
        {
            if (messages == null)
                return new List<ChatMessageDto>();

            List<ChatMessageDto> result = new List<ChatMessageDto>();

            foreach (MessageModel message in messages)
            {
                ClientDto? from = await GetClient(message.EmployeeFromId, false);
                ClientDto? to = await GetClient(message.EmployeeToId, false);

                if (from != null && to != null)
                {
                    SignalRChat.Core.Enums.MessageType messageType = GetMessageType(message.MessageType);

                    ChatMessageDto chatMessageDto = null!;

                    switch (messageType)
                    {
                        case SignalRChat.Core.Enums.MessageType.Text:
                            string? text = Converters.FromByteArray<string>(message.MessageContent);
                            if (text != null)
                                chatMessageDto = new TextChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, text);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Image:
                            chatMessageDto = new ImageChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageContent);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Record:
                            chatMessageDto = new RecordChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageContent);
                            break;
                        case SignalRChat.Core.Enums.MessageType.Stream:
                        case SignalRChat.Core.Enums.MessageType.Buzz:
                        default:
                            chatMessageDto = new ChatMessageDto(Ulid.NewUlid(), from.ClientId, to.ClientId, message.MessageDate, messageType);
                            break;
                    }
                    if (chatMessageDto != null)
                    {
                        chatMessageDto.SetDateTime(message.MessageDate);
                        result.Add(chatMessageDto);
                    }
                }
            }

            return result;
        }

        private SignalRChat.Core.Enums.MessageType GetMessageType(int messageType)
        {
            switch (messageType)
            {
                case 1: return SignalRChat.Core.Enums.MessageType.Text;
                case 2: return SignalRChat.Core.Enums.MessageType.Image;
                case 3: return SignalRChat.Core.Enums.MessageType.Record;
                case 4: return SignalRChat.Core.Enums.MessageType.Stream;
                case 5: return SignalRChat.Core.Enums.MessageType.Buzz;
                default: return SignalRChat.Core.Enums.MessageType.Text;
            }
        }

        public async Task<Dictionary<string, ClientDto>> GetConnectedClients()
        {
            var users = await _repository.GetClientsByType<Client, ClientDto>();
            return users.Cast<ClientDto>().ToDictionary(x => x.Name);
        }

        // Called by client
        public Task Pong()
        {
            CurrentConnections.AddOrUpdate(
                ValidateValue.ValidateUlid(GetUserId),
                (Context.ConnectionId, DateTime.UtcNow),
                (id, existingConnectionId) =>
                {
                    return existingConnectionId;
                });
            return Task.CompletedTask;
        }

        // Periodically called to check health
        public async Task PingClientsAsync(IHubContext<LeadTeamsHub> hubContext)
        {
            await hubContext.Clients.All.SendCoreAsync(SignalRChatIntegrationHelper.HUBs.Ping, new object[] { });
        }

        public static IEnumerable<(Ulid employeeId, string ConnectionId, bool IsAlive)> GetClientHealthStatus(TimeSpan timeout)
        {
            var now = DateTime.UtcNow;
            return CurrentConnections
                .Select(kv => (kv.Key, kv.Value.Item1, now - kv.Value.Item2 < timeout));
        }
    }
}
