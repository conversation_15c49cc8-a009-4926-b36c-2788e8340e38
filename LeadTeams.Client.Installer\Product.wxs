<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  
  <Package Name="$(ProductName)" 
           Version="$(ProductVersion)" 
           Manufacturer="$(Manufacturer)" 
           UpgradeCode="$(UpgradeCode)"
           Language="1033"
           Codepage="1252"
           InstallerVersion="500"
           Compressed="yes"
           InstallScope="perMachine">

    <!-- Upgrade logic to replace existing installations -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed."
                  Schedule="afterInstallInitialize"
                  AllowSameVersionUpgrades="yes" />

    <!-- Media definition -->
    <Media Id="1" Cabinet="LeadTeamsClient.cab" EmbedCab="yes" />

    <!-- Installation directory structure -->
    <StandardDirectory Id="ProgramFiles64Folder">
      <Directory Id="ManufacturerFolder" Name="LeadTeams">
        <Directory Id="INSTALLFOLDER" Name="LeadTeams Client" />
      </Directory>
    </StandardDirectory>

    <!-- Desktop and Start Menu folders -->
    <StandardDirectory Id="DesktopFolder" />
    <StandardDirectory Id="ProgramMenuFolder">
      <Directory Id="ApplicationProgramsFolder" Name="LeadTeams" />
    </StandardDirectory>

    <!-- Main feature -->
    <Feature Id="ProductFeature" Title="LeadTeams Client" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="DesktopShortcut" />
      <ComponentRef Id="TaskbarPin" />
      <ComponentRef Id="WindowsAppRegistration" />
    </Feature>

    <!-- Application shortcuts -->
    <Component Id="ApplicationShortcut" Directory="ApplicationProgramsFolder" Guid="*">
      <Shortcut Id="ApplicationStartMenuShortcut"
                Name="LeadTeams Client"
                Description="LeadTeams Client Application"
                Target="[INSTALLFOLDER]LeadTeams.Client.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="LeadTeamsIcon.ico" />
      <!-- Uninstall shortcut -->
      <Shortcut Id="UninstallShortcut"
                Name="Uninstall LeadTeams Client"
                Description="Uninstalls LeadTeams Client"
                Target="[SystemFolder]msiexec.exe"
                Arguments="/x [ProductCode]" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Client" Name="installed" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="*">
      <Shortcut Id="ApplicationDesktopShortcut"
                Name="LeadTeams Client"
                Description="LeadTeams Client Application"
                Target="[INSTALLFOLDER]LeadTeams.Client.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="LeadTeamsIcon.ico" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Client" Name="desktop_shortcut" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <!-- Taskbar pinning component -->
    <Component Id="TaskbarPin" Directory="INSTALLFOLDER" Guid="*">
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Client" Name="taskbar_pin" Type="integer" Value="1" KeyPath="yes" />
      <!-- Custom action to pin to taskbar will be added -->
    </Component>

    <!-- Icon definition -->
    <Icon Id="LeadTeamsIcon.ico" SourceFile="LeadTeamsIcon.ico" />

    <!-- Add/Remove Programs properties -->
    <Property Id="ARPPRODUCTICON" Value="LeadTeamsIcon.ico" />
    <Property Id="ARPHELPLINK" Value="https://leadteams.com" />
    <Property Id="ARPURLINFOABOUT" Value="https://leadteams.com" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />
    <Property Id="ARPSIZE" Value="50000" />
    <Property Id="ARPCOMMENTS" Value="LeadTeams Client Application for employee monitoring and tracking" />

    <!-- Windows 10/11 App Registration -->
    <Component Id="WindowsAppRegistration" Directory="INSTALLFOLDER" Guid="*">
      <RegistryKey Root="HKLM" Key="SOFTWARE\Classes\Applications\LeadTeams.Client.exe">
        <RegistryValue Name="FriendlyAppName" Value="LeadTeams Client" Type="string" />
        <RegistryValue Name="ApplicationCompany" Value="LeadTeams" Type="string" />
        <RegistryValue Name="ApplicationDescription" Value="LeadTeams Client Application" Type="string" />
        <RegistryValue Name="ApplicationIcon" Value="[INSTALLFOLDER]LeadTeams.Client.exe,0" Type="string" />
      </RegistryKey>
      <RegistryValue Root="HKLM" Key="SOFTWARE\Classes\Applications\LeadTeams.Client.exe" Name="" Value="" Type="string" KeyPath="yes" />
    </Component>

    <!-- Windows Apps integration -->
    <Property Id="ALLUSERS" Value="1" />

    <!-- Custom actions for taskbar pinning -->
    <CustomAction Id="PinToTaskbar"
                  Directory="INSTALLFOLDER"
                  ExeCommand="powershell.exe -WindowStyle Hidden -Command &quot;$shell = New-Object -ComObject Shell.Application; $folder = $shell.Namespace('[INSTALLFOLDER]'); $item = $folder.ParseName('LeadTeams.Client.exe'); $item.InvokeVerb('taskbarpin')&quot;"
                  Execute="deferred"
                  Impersonate="yes"
                  Return="ignore" />

    <CustomAction Id="UnpinFromTaskbar"
                  Directory="INSTALLFOLDER"
                  ExeCommand="powershell.exe -WindowStyle Hidden -Command &quot;$shell = New-Object -ComObject Shell.Application; $folder = $shell.Namespace('[INSTALLFOLDER]'); $item = $folder.ParseName('LeadTeams.Client.exe'); $item.InvokeVerb('taskbarunpin')&quot;"
                  Execute="deferred"
                  Impersonate="yes"
                  Return="ignore" />

    <!-- Install sequence -->
    <InstallExecuteSequence>
      <Custom Action="PinToTaskbar" After="InstallFinalize">NOT Installed</Custom>
    </InstallExecuteSequence>

    <!-- Uninstall sequence -->
    <InstallExecuteSequence>
      <Custom Action="UnpinFromTaskbar" Before="RemoveFiles">Installed AND NOT REINSTALL</Custom>
    </InstallExecuteSequence>

  </Package>
</Wix>
