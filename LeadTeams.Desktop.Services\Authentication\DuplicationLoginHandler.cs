﻿namespace LeadTeams.Desktop.Services.Authentication
{
    public static class DuplicationLoginHandler
    {
        private static bool _subscribed = false;
        public static void Subscribe()
        {
            if (_subscribed) return;
            _subscribed = true;
            LeadTeamsSignalRConnectionManager.OnDuplicationLogin += ShowDuplicationMessageAndExit;
        }

        private static void ShowDuplicationMessageAndExit()
        {
            // Disable subscription for now, as it is not needed in the desktop application
            // Always show on top, close all forms
            //foreach (Form form in Application.OpenForms)
            //{
            //    if (form.InvokeRequired)
            //    {
            //        form.Invoke(new Action(() => ShowAndExit(form)));
            //    }
            //    else
            //    {
            //        ShowAndExit(form);
            //    }
            //}
        }

        private static void ShowAndExit(Form form)
        {
            MessageBox.Show(form, "You have been logged in from another device, please log in again.", "Login Duplication", MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1);
            Application.Exit();
        }
    }
}
