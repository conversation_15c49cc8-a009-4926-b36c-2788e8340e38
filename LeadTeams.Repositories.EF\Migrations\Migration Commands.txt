﻿Add-Migration 'LeadTeams_003' -OutputDir Migrations\MySQLMigrations -Args 'MySQL' -Context MySQLApplicationDBContext -Project LeadTeams.Repositories.EF
Update-Database -Args 'MySQL' -Context MySQLApplicationDBContext

Add-Migration 'LeadTeams_003' -OutputDir Migrations\SQLiteMigrations -Args 'SQLite' -Context SQLiteApplicationDBContext -Project LeadTeams.Repositories.EF
Update-Database -Args 'SQLite' -Context SQLiteApplicationDBContext

///////

Update-Database 'LeadTeams_002' -Args 'MySQL' -Context MySQLApplicationDBContext
Remove-Migration -Args 'MySQL' -Context MySQLApplicationDBContext -Project LeadTeams.Repositories.EF

Update-Database 'LeadTeams_002' -Args 'SQLite' -Context SQLiteApplicationDBContext
Remove-Migration -Args 'SQLite' -Context SQLiteApplicationDBContext -Project LeadTeams.Repositories.EF


