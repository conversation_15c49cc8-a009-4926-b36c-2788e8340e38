﻿namespace LeadTeams.Models.Model
{
    [Table("LoginSession")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<LoginSessionModel>))]
    public class LoginSessionModel : BaseOrganizationWithoutTrackingModel
    {
        private string _DeviceId = null!;
        private string _SessionToken = null!;
        private DateTime _SessionDateTime = DateTime.Now;
        private DateTime _SessionLastDateTime = DateTime.Now;
        private string _SessionStatus = SessionStatusEnumeration.Active.Name;
        private string _SessionHostType = SessionHostTypeEnumeration.Unknown.Name;
        private string _SessionAppName = null!;

        [CustomRequired]
        [DisplayName("Device Id")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string DeviceId { get => _DeviceId; set => this.CheckPropertyChanged(ref _DeviceId, ref value); }
        [CustomRequired]
        [DisplayName("Session Token")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string SessionToken { get => _SessionToken; set => this.CheckPropertyChanged(ref _SessionToken, ref value); }
        [CustomRequired]
        [DisplayName("Session DateTime")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime SessionDateTime { get => _SessionDateTime; set => this.CheckPropertyChanged(ref _SessionDateTime, ref value); }
        [CustomRequired]
        [DisplayName("Session Last DateTime")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime SessionLastDateTime { get => _SessionLastDateTime; set => this.CheckPropertyChanged(ref _SessionLastDateTime, ref value); }
        [CustomRequired]
        [DisplayName("Session Status")]
        [MaxLength(10)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(SessionStatusEnumeration))]
        public string SessionStatus { get => _SessionStatus; set => this.CheckPropertyChanged(ref _SessionStatus, ref value); }
        [CustomRequired]
        [DisplayName("Session Host Type")]
        [MaxLength(10)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(SessionHostTypeEnumeration))]
        public string SessionHostType { get => _SessionHostType; set => this.CheckPropertyChanged(ref _SessionHostType, ref value); }
        [CustomRequired]
        [DisplayName("Session App Name")]
        [MaxLength(25)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(SessionHostTypeEnumeration))]
        public string SessionAppName { get => _SessionAppName; set => this.CheckPropertyChanged(ref _SessionAppName, ref value); }

        [CustomRequired]
        [Browsable(false)]
        public Ulid EmployeeId { get; set; }
        [Browsable(false)]
        public virtual EmployeeModel Employee { get; set; } = null!;
    }
}
