﻿namespace LeadTeams.Services.Server.Business
{
    public class LoginSessionService : ILoginSessionService
    {
        private readonly IUnitOfWork _unitOfWork;
        public LoginSessionService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<LoginSessionModel>> GetActiveLoginSessionsAsync()
        {
            try
            {
                RepositorySpecifications<LoginSessionModel> specifications = new RepositorySpecifications<LoginSessionModel>()
                {
                    SearchValue = x => x.SessionStatus == SessionStatusEnumeration.Active.Name,
                    IsTackable = false,
                };

                return await _unitOfWork.LoginSession.GetAllAsync(specifications);
            }
            catch (Exception ex)
            {
                return new List<LoginSessionModel>();
            }
        }

        public async Task<List<LoginSessionModel>> GetLoginSessions(Ulid userId, string appName)
        {
            try
            {
                RepositorySpecifications<LoginSessionModel> specifications = new RepositorySpecifications<LoginSessionModel>()
                {
                    SearchValue = x => x.EmployeeId == userId && x.SessionAppName == appName && x.SessionStatus.Equals(SessionStatusEnumeration.Active.Name),
                    IsTackable = false,
                };

                return await _unitOfWork.LoginSession.GetAllAsync(specifications);
            }
            catch (Exception ex)
            {
                return new List<LoginSessionModel>();
            }
        }

        public async Task<LoginSessionModel?> GetLoginSessionWithDeviceId(Ulid userId, string appName, string deviceId)
        {
            try
            {
                RepositorySpecifications<LoginSessionModel> specifications = new RepositorySpecifications<LoginSessionModel>()
                {
                    SearchValue = x => x.EmployeeId == userId && x.SessionAppName == appName && x.DeviceId == deviceId,
                    IsTackable = false,
                };

                return await _unitOfWork.LoginSession.GetAsync(specifications);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<LoginSessionModel?> GetLoginSessionWithSessionToken(Ulid userId, string appName, string sessionToken)
        {
            try
            {
                var loginSessions = await GetLoginSessions(userId, appName);
                return loginSessions.FirstOrDefault(x => x.SessionToken == sessionToken);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task AddLoginSession(LoginSessionModel loginSession)
        {
            try
            {
                new BaseValidation().Validate(loginSession);
                await _unitOfWork.LoginSession.AddAsync(loginSession);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task UpdateLoginSession(LoginSessionModel loginSession)
        {
            try
            {
                new BaseValidation().Validate(loginSession);
                await _unitOfWork.LoginSession.UpdateAsync(loginSession);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task InactiveLoginSession(Ulid userId, string appName, string sessionToken)
        {
            try
            {
                var loginSession = await GetLoginSessionWithSessionToken(userId, appName, sessionToken);
                if (loginSession != null)
                {
                    loginSession.SessionStatus = SessionStatusEnumeration.Inactive.Name;
                    await _unitOfWork.LoginSession.UpdateAsync(loginSession);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task RemoveLoginSession(Ulid userId, string appName, string sessionToken)
        {
            try
            {
                var loginSession = await GetLoginSessionWithSessionToken(userId, appName, sessionToken);
                if (loginSession != null)
                    await _unitOfWork.LoginSession.RemoveAsync(loginSession);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
