<Project Sdk="WixToolset.Sdk/6.0.1">
  <PropertyGroup>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>
  <PropertyGroup>
    <OutputName>LeadTeamsDesktopSetup</OutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DefineConstants>Debug</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Product.wxs" />
    <Compile Include="Components.wxs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="LeadTeamsIcon.ico" />
    <Content Include="License.rtf" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LeadTeams.Desktop\LeadTeams.Desktop.csproj">
      <Name>LeadTeams.Desktop</Name>
      <Project>{bb738529-6aa7-4d82-b8db-13b4e1284f54}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
</Project>