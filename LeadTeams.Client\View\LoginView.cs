namespace LeadTeams.Client.View
{
    public partial class LoginView : LeadTeamsForm
    {
        private readonly ISession _session;
        private readonly IAuthenticationService _service;
        private readonly ILoginService _loginService;

        public LoginView() : base()
        {
            InitializeComponent();
        }

        public LoginView(ISession session, IAuthenticationService service, ILoginService loginService)
        {
            InitializeComponent();

            _session = session;
            _service = service;
            _loginService = loginService;

            DuplicationLoginHandler.Subscribe();

            _session.AppLogger.LogInformation($"Setting variables");

            _session.AppLogger.LogInformation($"Inject SignalRChat LoginPresenter");

            _session.AppLogger.LogInformation($"Associate And Raise Events");
            AssociateAndRaiseViewEvents();
        }

        private void AssociateAndRaiseViewEvents()
        {
            // Use AsyncButtonHandler with loading indicators to provide visual feedback
            this.btnLogin.HandleAsyncClickWithIndicator(
                asyncAction: async () =>
                {
                    Properties.Settings.Default.SaveLoginData = cbxSaveLoginData.Checked;
                    Properties.Settings.Default.UserName = txtUserName.Text;
                    Properties.Settings.Default.Password = txtPassword.Text;
                    Properties.Settings.Default.Save();
                    Properties.Settings.Default.Reload();

                    // Call the async login method
                    await PerformLoginAsync();
                },
                loadingText: "Logging in...",
                errorHandler: ex =>
                {
                    // Handle any exceptions
                    MessageBox.Show(ex.Message);
                    _session.AppLogger.LogError(ex, $"Login error: {ex.Message}");
                }
            );

            // Regular click handler for exit button (no async operation)
            btnExit.Click += (s, e) =>
            {
                Environment.Exit(0);
            };

            this.Load += (s, e) =>
            {
                if (!Debugger.IsAttached && Properties.Settings.Default.SaveLoginData)
                {
                    cbxSaveLoginData.Checked = Properties.Settings.Default.SaveLoginData;
                    txtUserName.Text = Properties.Settings.Default.UserName;
                    txtPassword.Text = Properties.Settings.Default.Password;
                    btnLogin.PerformClick();
                }
                if (Debugger.IsAttached)
                {
                    txtUserName.Text = "<EMAIL>";
                    txtPassword.Text = "123456";
                    btnLogin.PerformClick();
                }
            };
        }

        /// <summary>
        /// Performs the login operation asynchronously.
        /// </summary>
        private async Task PerformLoginAsync()
        {
            LoginRequest login = new LoginRequest()
            {
                UserName = txtUserName.Text,
                Password = txtPassword.Text,
            };

            _session.AppLogger.LogDebug($"Logging in to user: [{login.UserName}]");
            LoginSessionResponse loginSession = await _service.Login(login);

            if (loginSession != null)
            {
                if (!Debugger.IsAttached)
                    MessageBox.Show("You have been logged in successfully");

                _session.SetEmployee(loginSession);
                await SetEmployee(loginSession);

                // Wait a short period to allow duplication event to be processed
                await Task.Delay(1200);

                EmployeeTrackerView? employeeTrackerView = GetInjectedServices.GetFormByType<EmployeeTrackerView>();
                if (employeeTrackerView != null)
                {
                    employeeTrackerView.Show();
                    this.Close();
                }
            }
        }

        // Keep the original method for backward compatibility
        private async void CheckLogin(object? sender, EventArgs e)
        {
            try
            {
                await PerformLoginAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private async Task SetEmployee(LoginSessionResponse loginSession)
        {
            IDictionary<string, string> headers = new Dictionary<string, string>();

            headers.TryAdd(AuthenticationDefaults.AuthenticationHeader, $"Bearer {loginSession.Token}");
            headers.TryAdd("UserId", loginSession.Employee.Id.ToString());
            headers.TryAdd("UserName", loginSession.Employee.EmployeeName.ToString());
            headers.TryAdd("OrganizationId", _session.Organization.Id.ToString());
            if (GetMacAddress() is string macAddress && !string.IsNullOrEmpty(macAddress))
                headers.TryAdd("DeviceId", macAddress);
            headers.TryAdd("HostType", SessionHostTypeEnumeration.Desktop.Name);
            headers.TryAdd("AppName", "ClientApp");

            SignalRConnectionManagerOptions signalRConnectionManagerOptions = new SignalRConnectionManagerOptions
            {
                UserId = loginSession.Employee.Id,
                UserName = loginSession.Employee.EmployeeName.ToString(),
                Url = SignalRChatIntegrationHelper.URLs.LeadTeams,
                Headers = headers,
            };

            await _loginService.SendAsync(signalRConnectionManagerOptions);
        }

        private static string? GetMacAddress()
        {
            foreach (var nic in System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces())
            {
                if (nic.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                    nic.NetworkInterfaceType != System.Net.NetworkInformation.NetworkInterfaceType.Loopback)
                {
                    var address = nic.GetPhysicalAddress()?.ToString();
                    if (!string.IsNullOrWhiteSpace(address))
                        return address;
                }
            }
            return null;
        }
    }
}
