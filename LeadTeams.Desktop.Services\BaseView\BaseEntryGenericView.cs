﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseEntryGenericView<TEntity, TEntityView, TEntityCreate, TEntityUpdate> : BaseEntryView
        where TEntity : BaseIdentityModel
        where TEntityView : BaseIdentityModel, new()
        where TEntityCreate : BaseOrganizationCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseOrganizationUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
    {
        private readonly ILeadTeamsBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> _baseService;

        //For design time support
        public BaseEntryGenericView() : base()
        {
            InitializeComponent();
        }

        public BaseEntryGenericView(ILeadTeamsBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> baseService)
        {
            _baseService = baseService;
            InitializeComponent();
        }

        #region Buttons Actions Events
        protected override void ClearAndNew()
        {
            base.ClearAndNew();
        }

        protected override void NewFunction()
        {
            base.NewFunction();
            ClearAndNew();
        }

        protected override void SaveFunction()
        {
            try
            {
                base.SaveFunction();

                if (Id != Ulid.Empty)
                    IsEdit = _baseService.IsExist(Id).Data;
                else
                    IsEdit = false;

                EditModelBeforeSave();

                if (IsEdit)
                {
                    var result = _baseService.Update(UpdateModel);
                    result.ThrowIfFailure();
                    MessageBox.Show("Modified successfully");
                }
                else
                {
                    var result = _baseService.Add(CreateModel);
                    result.ThrowIfFailure();
                    MessageBox.Show("Saved successfully");
                }
                SuspendDataChanged = true;
                NewFunction();
                SuspendDataChanged = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        protected override void DeleteFunction()
        {
            try
            {
                base.DeleteFunction();

                var result = MessageBox.Show("Do you want to delete data ?", "Warning!", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                    if (Id != Ulid.Empty)
                        _baseService.Remove(Id);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
        #endregion

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        private TEntityCreate CreateModel { get => CreateEntity(); set => SetCreateEntity(value); }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        private TEntityUpdate UpdateModel { get => UpdateEntity(); set => SetUpdateEntity(value); }

        protected virtual TEntityCreate CreateEntity()
        {
            throw new NotImplementedException();
        }

        protected virtual void SetCreateEntity(TEntityCreate model)
        {
            IsEdit = true;
        }

        protected virtual TEntityUpdate UpdateEntity()
        {
            throw new NotImplementedException();
        }

        protected virtual void SetUpdateEntity(TEntityUpdate model)
        {
            Id = model.Id;
            IsEdit = true;
        }
    }
}
